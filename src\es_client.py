"""
Elasticsearch客户端模块
负责ES连接、查询和数据写入操作
"""

import logging
from typing import List, Dict, Any, Optional
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk


class ESClient:
    """Elasticsearch客户端类"""
    
    def __init__(self, host: str):
        """
        初始化ES客户端
        
        Args:
            host: ES主机地址
        """
        self.host = host
        self.client = None
        self.logger = logging.getLogger(__name__)
        
    def connect(self) -> bool:
        """
        连接到ES集群
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.client = Elasticsearch([self.host])
            # 测试连接
            if self.client.ping():
                self.logger.info(f"成功连接到ES: {self.host}")
                return True
            else:
                self.logger.error(f"无法连接到ES: {self.host}")
                return False
        except Exception as e:
            self.logger.error(f"连接ES时发生错误: {e}")
            return False
    
    def query_index_data(self, index_name: str, fields: List[str], 
                        date_field: str = "TryDate") -> List[Dict[str, Any]]:
        """
        查询指定index的数据
        
        Args:
            index_name: index名称
            fields: 需要查询的字段列表
            date_field: 日期字段名
            
        Returns:
            List[Dict]: 查询结果列表
        """
        try:
            # 构建查询的字段列表
            source_fields = ["IP", "Owner", "Version", date_field] + fields
            
            # 构建查询体
            query_body = {
                "query": {"match_all": {}},
                "sort": [{date_field: {"order": "asc"}}],
                "_source": source_fields,
                "size": 10000  # 可以根据需要调整
            }
            
            self.logger.info(f"开始查询index: {index_name}")
            
            # 使用scroll API处理大量数据
            results = []
            response = self.client.search(
                index=index_name,
                body=query_body,
                scroll='2m'
            )
            
            scroll_id = response['_scroll_id']
            hits = response['hits']['hits']
            
            while hits:
                for hit in hits:
                    results.append(hit['_source'])
                
                # 获取下一批数据
                response = self.client.scroll(
                    scroll_id=scroll_id,
                    scroll='2m'
                )
                scroll_id = response['_scroll_id']
                hits = response['hits']['hits']
            
            # 清理scroll
            self.client.clear_scroll(scroll_id=scroll_id)
            
            self.logger.info(f"查询完成，共获取 {len(results)} 条记录")
            return results
            
        except Exception as e:
            self.logger.error(f"查询index {index_name} 时发生错误: {e}")
            return []
    
    def create_index_if_not_exists(self, index_name: str) -> bool:
        """
        创建index（如果不存在）
        
        Args:
            index_name: index名称
            
        Returns:
            bool: 创建是否成功
        """
        try:
            if not self.client.indices.exists(index=index_name):
                # 定义index mapping
                mapping = {
                    "mappings": {
                        "properties": {
                            "start_date": {"type": "date"},
                            "end_date": {"type": "date"},
                            "IP": {"type": "keyword"},
                            "Owner": {"type": "keyword"},
                            "Version": {"type": "keyword"},
                            "key_name": {"type": "keyword"},
                            "start_value": {"type": "double"},
                            "end_value": {"type": "double"},
                            "change_rate": {"type": "double"},
                            "created_at": {"type": "date"},
                            "source_index": {"type": "keyword"}
                        }
                    }
                }
                
                self.client.indices.create(index=index_name, body=mapping)
                self.logger.info(f"成功创建index: {index_name}")
            else:
                self.logger.info(f"Index已存在: {index_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建index {index_name} 时发生错误: {e}")
            return False
    
    def bulk_insert_data(self, index_name: str, data_list: List[Dict[str, Any]]) -> bool:
        """
        批量插入数据
        
        Args:
            index_name: 目标index名称
            data_list: 要插入的数据列表
            
        Returns:
            bool: 插入是否成功
        """
        try:
            if not data_list:
                self.logger.warning("没有数据需要插入")
                return True
            
            # 准备批量插入的数据
            actions = []
            for data in data_list:
                action = {
                    "_index": index_name,
                    "_source": data
                }
                actions.append(action)
            
            # 执行批量插入
            success, failed = bulk(self.client, actions)
            
            self.logger.info(f"批量插入完成: 成功 {success} 条，失败 {len(failed)} 条")
            
            if failed:
                self.logger.error(f"插入失败的记录: {failed}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"批量插入数据时发生错误: {e}")
            return False
