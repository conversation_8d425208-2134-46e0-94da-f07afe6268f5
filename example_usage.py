#!/usr/bin/env python3
"""
ES监控系统使用示例
演示如何使用监控系统的各个组件
"""

import json
from datetime import datetime, timedelta
from src.monitor import ESMonitor
from src.utils import setup_logging


def create_sample_config():
    """创建示例配置文件"""
    sample_config = {
        "es_host": "http://***************:9200",
        "threshold_value": 0.02,  # 2%阈值
        "output_index": "psis-collector-change-monitor",
        "date_field": "TryDate",
        "indices_config": [
            {
                "index_name": "psis-collector-cpu-index",
                "fields": [
                    "cpu_usage",
                    "mem_usage",
                    "cpu_kmalloc_512_active_objs",
                    "cpu_kmalloc_512_num_objs"
                ]
            },
            {
                "index_name": "psis-collector-harddisk-index",
                "fields": [
                    "mmcblk0p1_Avail",
                    "mmcblk0p2_Avail",
                    "tmpfs_sysfscgroupUsed"
                ]
            }
        ]
    }
    
    with open("config/sample_config.json", "w", encoding="utf-8") as f:
        json.dump(sample_config, f, indent=2, ensure_ascii=False)
    
    print("示例配置文件已创建: config/sample_config.json")


def demonstrate_monitor_usage():
    """演示监控器的使用"""
    print("\n演示监控器使用:")
    print("-" * 30)
    
    # 使用默认配置
    config_path = "config/monitor_config.json"
    
    # 创建监控器实例
    monitor = ESMonitor(config_path)
    
    # 获取统计信息
    stats = monitor.get_statistics()
    print(f"配置加载状态: {stats['config_loaded']}")
    print(f"ES连接状态: {stats['es_connected']}")
    
    # 加载配置
    if monitor.load_config():
        print("配置加载成功")
        
        # 显示配置信息
        config = monitor.config
        print(f"ES主机: {config['es_host']}")
        print(f"阈值: {config['threshold_value'] * 100}%")
        print(f"输出index: {config['output_index']}")
        print(f"监控index数量: {len(config['indices_config'])}")
        
        for idx_config in config['indices_config']:
            index_name = idx_config['index_name']
            fields = idx_config['fields']
            print(f"  - {index_name}: {len(fields)} 个字段")
    else:
        print("配置加载失败")


def show_expected_output_format():
    """显示预期的输出格式"""
    print("\n预期输出数据格式:")
    print("-" * 30)
    
    sample_output = {
        "start_date": "2024-01-01T08:00:00",
        "end_date": "2024-01-02T08:00:00",
        "IP": "*************",
        "Owner": "system",
        "Version": "1.0",
        "key_name": "cpu_usage",
        "start_value": 50.0,
        "end_value": 55.0,
        "change_rate": 0.1,  # 10%增长
        "created_at": datetime.now().isoformat(),
        "source_index": "psis-collector-cpu-index"
    }
    
    print(json.dumps(sample_output, indent=2, ensure_ascii=False))


def show_usage_instructions():
    """显示使用说明"""
    print("\n使用说明:")
    print("-" * 30)
    print("1. 确保ES服务器可访问")
    print("2. 检查配置文件 config/monitor_config.json")
    print("3. 运行监控: python main.py")
    print("4. 查看结果: 检查输出index中的数据")
    print("\n配置说明:")
    print("- es_host: ES服务器地址")
    print("- threshold_value: 变化率阈值 (0.02 = 2%)")
    print("- output_index: 结果存储的index名称")
    print("- date_field: 时间字段名称 (默认: TryDate)")
    print("- indices_config: 要监控的index和字段配置")


def main():
    """主函数"""
    print("=" * 50)
    print("ES监控系统使用示例")
    print("=" * 50)
    
    # 设置日志
    setup_logging("INFO")
    
    # 创建示例配置
    create_sample_config()
    
    # 演示监控器使用
    demonstrate_monitor_usage()
    
    # 显示输出格式
    show_expected_output_format()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n" + "=" * 50)
    print("示例演示完成")
    print("=" * 50)


if __name__ == "__main__":
    main()
