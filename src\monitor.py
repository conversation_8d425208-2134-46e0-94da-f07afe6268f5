"""
监控主逻辑模块
协调各个组件完成监控任务
"""

import json
import logging
from typing import Dict, Any, List
from .es_client import ESClient
from .data_processor import DataProcessor
from .utils import setup_logging


class ESMonitor:
    """ES监控器主类"""
    
    def __init__(self, config_path: str):
        """
        初始化监控器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = None
        self.es_client = None
        self.data_processor = None
        self.logger = logging.getLogger(__name__)
        
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.logger.info(f"成功加载配置文件: {self.config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
    
    def initialize_components(self) -> bool:
        """
        初始化各个组件
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化ES客户端
            es_host = self.config.get("es_host")
            self.es_client = ESClient(es_host)
            
            if not self.es_client.connect():
                return False
            
            # 初始化数据处理器
            threshold_value = self.config.get("threshold_value", 0.02)
            date_field = self.config.get("date_field", "TryDate")
            self.data_processor = DataProcessor(threshold_value, date_field)
            
            self.logger.info("组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            return False
    
    def process_single_index(self, index_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理单个index的数据
        
        Args:
            index_config: index配置信息
            
        Returns:
            List[Dict]: 变化记录列表
        """
        index_name = index_config.get("index_name")
        monitor_fields = index_config.get("fields", [])
        
        self.logger.info(f"开始处理index: {index_name}")
        
        # 步骤1: 查询原始数据
        raw_data = self.es_client.query_index_data(
            index_name=index_name,
            fields=monitor_fields,
            date_field=self.config.get("date_field", "TryDate")
        )
        
        if not raw_data:
            self.logger.warning(f"Index {index_name} 没有查询到数据")
            return []
        
        # 步骤2: 过滤每日第一条记录
        filtered_data = self.data_processor.filter_daily_first_records(raw_data)
        
        if not filtered_data:
            self.logger.warning(f"Index {index_name} 过滤后没有有效数据")
            return []
        
        # 步骤3: 计算变化率
        change_records = self.data_processor.calculate_change_rates(
            filtered_data=filtered_data,
            monitor_fields=monitor_fields,
            source_index=index_name
        )
        
        self.logger.info(f"Index {index_name} 处理完成，生成 {len(change_records)} 条变化记录")
        return change_records
    
    def run_monitor(self) -> bool:
        """
        运行监控任务
        
        Returns:
            bool: 运行是否成功
        """
        try:
            self.logger.info("开始执行ES监控任务")
            
            # 加载配置
            if not self.load_config():
                return False
            
            # 初始化组件
            if not self.initialize_components():
                return False
            
            # 处理所有index
            all_change_records = []
            indices_config = self.config.get("indices_config", [])
            
            for index_config in indices_config:
                change_records = self.process_single_index(index_config)
                all_change_records.extend(change_records)
            
            # 创建输出index
            output_index = self.config.get("output_index", "psis-collector-change-monitor")
            if not self.es_client.create_index_if_not_exists(output_index):
                self.logger.error("创建输出index失败")
                return False
            
            # 写入变化记录
            if all_change_records:
                if self.es_client.bulk_insert_data(output_index, all_change_records):
                    self.logger.info(f"监控任务完成，共写入 {len(all_change_records)} 条变化记录到 {output_index}")
                else:
                    self.logger.error("写入变化记录失败")
                    return False
            else:
                self.logger.info("没有检测到超过阈值的变化记录")
            
            return True
            
        except Exception as e:
            self.logger.error(f"监控任务执行失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取监控统计信息
        
        Returns:
            Dict: 统计信息
        """
        # 这里可以添加统计信息的获取逻辑
        # 比如查询输出index的记录数量等
        return {
            "config_loaded": self.config is not None,
            "es_connected": self.es_client is not None and self.es_client.client is not None,
            "threshold_value": self.config.get("threshold_value") if self.config else None,
            "monitored_indices": len(self.config.get("indices_config", [])) if self.config else 0
        }
