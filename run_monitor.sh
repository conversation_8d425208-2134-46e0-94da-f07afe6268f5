#!/bin/bash

echo "================================================"
echo "ES监控系统启动脚本"
echo "================================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "config/monitor_config.json" ]; then
    echo "错误: 配置文件不存在 - config/monitor_config.json"
    echo "请先创建配置文件"
    exit 1
fi

# 安装依赖包
echo "检查并安装依赖包..."
pip3 install -r requirements.txt

# 运行监控系统
echo ""
echo "启动ES监控系统..."
echo ""
python3 main.py

echo ""
echo "监控任务完成"
