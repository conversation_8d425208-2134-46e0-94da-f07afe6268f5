"""
数据处理模块
负责数据过滤、变化率计算等核心业务逻辑
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from collections import defaultdict
from .utils import parse_date, get_date_key, is_numeric_value, calculate_change_rate


class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, threshold_value: float, date_field: str = "TryDate"):
        """
        初始化数据处理器
        
        Args:
            threshold_value: 变化率阈值
            date_field: 日期字段名
        """
        self.threshold_value = threshold_value
        self.date_field = date_field
        self.logger = logging.getLogger(__name__)
    
    def filter_daily_first_records(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤每个IP每天的第一条记录
        
        Args:
            raw_data: 原始数据列表
            
        Returns:
            List[Dict]: 过滤后的数据列表
        """
        self.logger.info("开始过滤每日第一条记录")
        
        # 按IP和日期分组
        ip_date_groups = defaultdict(list)
        
        for record in raw_data:
            # 解析日期
            try_date = parse_date(record.get(self.date_field))
            if try_date is None:
                self.logger.warning(f"记录的日期字段解析失败: {record}")
                continue
            
            ip = record.get("IP")
            if not ip:
                self.logger.warning(f"记录缺少IP字段: {record}")
                continue
            
            # 生成分组键：IP + 日期
            date_key = get_date_key(try_date)
            group_key = f"{ip}_{date_key}"
            
            # 添加解析后的日期时间到记录中
            record_with_datetime = record.copy()
            record_with_datetime['_parsed_datetime'] = try_date
            record_with_datetime['_date_key'] = date_key
            
            ip_date_groups[group_key].append(record_with_datetime)
        
        # 对每个分组按时间排序，取第一条
        filtered_records = []
        for group_key, records in ip_date_groups.items():
            # 按解析后的日期时间排序
            records.sort(key=lambda x: x['_parsed_datetime'])
            first_record = records[0]
            
            # 移除临时添加的字段
            first_record.pop('_parsed_datetime', None)
            first_record.pop('_date_key', None)
            
            filtered_records.append(first_record)
        
        self.logger.info(f"过滤完成，从 {len(raw_data)} 条记录中筛选出 {len(filtered_records)} 条每日第一条记录")
        return filtered_records
    
    def calculate_change_rates(self, filtered_data: List[Dict[str, Any]], 
                             monitor_fields: List[str], 
                             source_index: str) -> List[Dict[str, Any]]:
        """
        计算变化率并筛选超过阈值的记录
        
        Args:
            filtered_data: 过滤后的数据
            monitor_fields: 需要监控的字段列表
            source_index: 来源index名称
            
        Returns:
            List[Dict]: 变化记录列表
        """
        self.logger.info(f"开始计算变化率，监控字段: {monitor_fields}")
        
        # 按IP分组
        ip_groups = defaultdict(list)
        for record in filtered_data:
            ip = record.get("IP")
            if ip:
                # 添加解析后的日期
                try_date = parse_date(record.get(self.date_field))
                if try_date:
                    record_with_date = record.copy()
                    record_with_date['_parsed_date'] = try_date
                    ip_groups[ip].append(record_with_date)
        
        change_records = []
        
        # 对每个IP的数据进行处理
        for ip, records in ip_groups.items():
            # 按日期排序
            records.sort(key=lambda x: x['_parsed_date'])
            
            # 计算相邻两天的变化率
            for i in range(len(records) - 1):
                start_record = records[i]
                end_record = records[i + 1]
                
                # 对每个监控字段计算变化率
                for field_name in monitor_fields:
                    change_record = self._calculate_field_change_rate(
                        start_record, end_record, field_name, source_index
                    )
                    
                    if change_record:
                        change_records.append(change_record)
        
        self.logger.info(f"变化率计算完成，共生成 {len(change_records)} 条变化记录")
        return change_records
    
    def _calculate_field_change_rate(self, start_record: Dict[str, Any], 
                                   end_record: Dict[str, Any], 
                                   field_name: str, 
                                   source_index: str) -> Optional[Dict[str, Any]]:
        """
        计算单个字段的变化率
        
        Args:
            start_record: 起始记录
            end_record: 结束记录
            field_name: 字段名
            source_index: 来源index
            
        Returns:
            Dict: 变化记录，如果不满足条件返回None
        """
        try:
            # 获取字段值
            start_value = start_record.get(field_name)
            end_value = end_record.get(field_name)
            
            # 检查值是否为数值
            if not is_numeric_value(start_value) or not is_numeric_value(end_value):
                return None
            
            start_value = float(start_value)
            end_value = float(end_value)
            
            # 计算变化率
            change_rate = calculate_change_rate(start_value, end_value)
            if change_rate is None:
                return None
            
            # 检查是否超过阈值（只关注增长超过阈值的情况）
            if change_rate <= self.threshold_value:
                return None
            
            # 构建变化记录
            change_record = {
                "start_date": start_record.get(self.date_field),
                "end_date": end_record.get(self.date_field),
                "IP": start_record.get("IP"),
                "Owner": start_record.get("Owner"),
                "Version": start_record.get("Version"),
                "key_name": field_name,
                "start_value": start_value,
                "end_value": end_value,
                "change_rate": change_rate,
                "created_at": datetime.now().isoformat(),
                "source_index": source_index
            }
            
            return change_record
            
        except Exception as e:
            self.logger.warning(f"计算字段 {field_name} 变化率时发生错误: {e}")
            return None
