"""
工具函数模块
包含日期处理、数据验证等通用功能
"""

import logging
from datetime import datetime
from typing import Any, Optional
from dateutil import parser


def parse_date(date_str: Any) -> Optional[datetime]:
    """
    解析日期字符串或时间戳
    
    Args:
        date_str: 日期字符串或时间戳
        
    Returns:
        datetime: 解析后的日期对象，解析失败返回None
    """
    if date_str is None:
        return None
    
    try:
        # 如果是数字，尝试作为时间戳解析
        if isinstance(date_str, (int, float)):
            # 判断是秒级还是毫秒级时间戳
            if date_str > 1e10:  # 毫秒级时间戳
                return datetime.fromtimestamp(date_str / 1000)
            else:  # 秒级时间戳
                return datetime.fromtimestamp(date_str)
        
        # 如果是字符串，使用dateutil解析
        if isinstance(date_str, str):
            return parser.parse(date_str)
        
        # 如果已经是datetime对象
        if isinstance(date_str, datetime):
            return date_str
            
        return None
        
    except Exception as e:
        logging.getLogger(__name__).warning(f"日期解析失败: {date_str}, 错误: {e}")
        return None


def get_date_key(dt: datetime) -> str:
    """
    获取日期的字符串键值（只包含年月日）
    
    Args:
        dt: 日期时间对象
        
    Returns:
        str: 格式为YYYY-MM-DD的日期字符串
    """
    return dt.strftime("%Y-%m-%d")


def is_numeric_value(value: Any) -> bool:
    """
    检查值是否为数值类型
    
    Args:
        value: 要检查的值
        
    Returns:
        bool: 是否为数值
    """
    if value is None:
        return False
    
    try:
        float(value)
        return True
    except (ValueError, TypeError):
        return False


def calculate_change_rate(start_value: float, end_value: float) -> Optional[float]:
    """
    计算变化率
    
    Args:
        start_value: 起始值
        end_value: 结束值
        
    Returns:
        float: 变化率，计算失败返回None
    """
    try:
        if start_value == 0:
            # 如果起始值为0，特殊处理
            if end_value == 0:
                return 0.0
            else:
                # 从0增长到非0值，返回一个很大的正值表示无限增长
                return float('inf') if end_value > 0 else float('-inf')
        
        change_rate = (end_value - start_value) / start_value
        return change_rate
        
    except Exception as e:
        logging.getLogger(__name__).warning(f"计算变化率失败: {start_value} -> {end_value}, 错误: {e}")
        return None


def setup_logging(level: str = "INFO") -> None:
    """
    设置日志配置
    
    Args:
        level: 日志级别
    """
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
