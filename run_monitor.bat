@echo off
echo ================================================
echo ES监控系统启动脚本
echo ================================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "config\monitor_config.json" (
    echo 错误: 配置文件不存在 - config\monitor_config.json
    echo 请先创建配置文件
    pause
    exit /b 1
)

REM 安装依赖包
echo 检查并安装依赖包...
pip install -r requirements.txt

REM 运行监控系统
echo.
echo 启动ES监控系统...
echo.
python main.py

echo.
echo 监控任务完成
pause
