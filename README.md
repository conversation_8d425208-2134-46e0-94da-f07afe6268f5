# ES监控系统

这是一个用于监控Elasticsearch数据变化趋势的Python系统。系统会分析指定index中各IP的监控指标，检测超过阈值的变化率，并将结果写入新的index。

## 功能特性

- **数据过滤**: 自动获取每个IP每天的第一条数据
- **变化率计算**: 计算相邻两天监控指标的变化率
- **阈值检测**: 筛选变化率超过设定阈值的记录
- **配置化管理**: 通过JSON文件灵活配置监控参数
- **批量处理**: 支持多个index和多个监控字段
- **结果存储**: 将检测结果存储到新的ES index中

## 项目结构

```
es_monitor_to_es/
├── config/
│   └── monitor_config.json          # 配置文件
├── src/
│   ├── __init__.py
│   ├── es_client.py                 # ES连接客户端
│   ├── data_processor.py            # 数据处理逻辑
│   ├── monitor.py                   # 主监控逻辑
│   └── utils.py                     # 工具函数
├── main.py                          # 主入口文件
├── requirements.txt                 # 依赖包
└── README.md                        # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

编辑 `config/monitor_config.json` 文件：

```json
{
  "es_host": "http://***************:9200",
  "threshold_value": 0.02,
  "output_index": "psis-collector-change-monitor",
  "date_field": "TryDate",
  "indices_config": [
    {
      "index_name": "psis-collector-cpu-index",
      "fields": ["cpu_usage", "mem_usage", ...]
    },
    {
      "index_name": "psis-collector-harddisk-index",
      "fields": ["mmcblk0p1_Avail", "mmcblk0p2_Avail", ...]
    }
  ]
}
```

### 配置参数说明

- `es_host`: Elasticsearch服务器地址
- `threshold_value`: 变化率阈值（0.02表示2%）
- `output_index`: 输出结果的index名称
- `date_field`: 时间字段名称（默认为TryDate）
- `indices_config`: 要监控的index配置列表
  - `index_name`: index名称
  - `fields`: 要监控的字段列表

## 运行方式

```bash
python main.py
```

## 输出数据结构

系统会在指定的输出index中创建以下结构的记录：

```json
{
  "start_date": "2024-01-01T00:00:00",
  "end_date": "2024-01-02T00:00:00", 
  "IP": "*************",
  "Owner": "system",
  "Version": "1.0",
  "key_name": "cpu_usage",
  "start_value": 50.0,
  "end_value": 55.0,
  "change_rate": 0.1,
  "created_at": "2024-01-02T10:30:00",
  "source_index": "psis-collector-cpu-index"
}
```

### 字段说明

- `start_date`: 起始日期（来自TryDate字段）
- `end_date`: 结束日期（来自TryDate字段）
- `IP`: IP地址
- `Owner`: 所有者
- `Version`: 版本信息
- `key_name`: 监控字段名称
- `start_value`: 起始值
- `end_value`: 结束值
- `change_rate`: 变化率（小数形式，0.1表示10%）
- `created_at`: 记录创建时间
- `source_index`: 来源index名称

## 工作流程

1. **数据查询**: 从配置的index中查询所有相关数据
2. **数据过滤**: 按IP和日期分组，获取每天的第一条记录
3. **变化率计算**: 计算相邻两天的字段变化率
4. **阈值筛选**: 筛选变化率超过阈值的记录
5. **结果存储**: 将筛选结果写入输出index

## 注意事项

- 确保ES服务器可访问
- 确保有足够的权限读取源index和创建新index
- 大量数据处理时注意内存使用
- 建议定期清理输出index中的历史数据

## 日志

系统会输出详细的运行日志，包括：
- 连接状态
- 数据处理进度
- 错误信息
- 统计结果
